server:
  servlet:
    context-path:
  port: 9081

spring:
  h2:
    console:
      path: /h2-console/semantic
      # enabled web
      enabled: true
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:semantic;DATABASE_TO_UPPER=false
    username: root
    password: semantic
  sql:
    init:
      schema-locations: classpath:db/semantic-schema-h2.sql
      data-locations: classpath:db/semantic-data-h2.sql

authentication:
  enable: true
  exclude:
    path: /api/auth/user/register,/api/auth/user/login
  token:
    http:
      header:
        key: Authorization
