package com.tencent.supersonic.headless.chat.knowledge.helper;

import com.google.common.collect.Lists;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.corpus.tag.Nature;
import com.hankcs.hanlp.dictionary.CoreDictionary;
import com.hankcs.hanlp.dictionary.DynamicCustomDictionary;
import com.hankcs.hanlp.seg.Segment;
import com.hankcs.hanlp.seg.common.Term;
import com.tencent.supersonic.common.pojo.enums.DictWordType;
import com.tencent.supersonic.headless.api.pojo.response.S2Term;
import com.tencent.supersonic.headless.chat.knowledge.DatabaseMapResult;
import com.tencent.supersonic.headless.chat.knowledge.DictWord;
import com.tencent.supersonic.headless.chat.knowledge.EmbeddingResult;
import com.tencent.supersonic.headless.chat.knowledge.HadoopFileIOAdapter;
import com.tencent.supersonic.headless.chat.knowledge.HanlpMapResult;
import com.tencent.supersonic.headless.chat.knowledge.MapResult;
import com.tencent.supersonic.headless.chat.knowledge.MultiCustomDictionary;
import com.tencent.supersonic.headless.chat.knowledge.SearchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * HanlpHelper 类是 Supersonic 系统中用于处理自然语言分词的工具类
 * 该类封装了对 HanLP 分词库的调用和配置，提供了词典管理、分词处理和结果转换等功能
 * 主要用于自然语言查询的分词处理，支持自定义词典的动态加载和管理
 */
@Slf4j
public class HanlpHelper {

    /**
     * 文件路径分隔符，使用系统默认分隔符
     */
    public static final String FILE_SPILT = File.separator;

    /**
     * 空格分隔符，用于分隔词性和频率等信息
     */
    public static final String SPACE_SPILT = "#";

    /**
     * 自定义词典实例，使用 volatile 保证多线程可见性
     */
    private static volatile DynamicCustomDictionary CustomDictionary;

    /**
     * 分词器实例，使用 volatile 保证多线程可见性
     */
    private static volatile Segment segment;

    /**
     * 静态初始化块，在类加载时重置 HanLP 配置
     */
    static {
        // 重置 HanLP 配置，设置正确的词典路径
        try {
            resetHanlpConfig();
        } catch (FileNotFoundException e) {
            log.error("resetHanlpConfig error", e);
        }
    }

    /**
     * 获取分词器实例，采用双重检查锁定模式确保线程安全
     *
     * @return 配置好的分词器实例
     */
    public static Segment getSegment() {
        if (segment == null) {
            synchronized (HanlpHelper.class) {
                if (segment == null) {
                    // 创建并配置分词器，启用索引模式，自定义词典等
                    segment = HanLP.newSegment().enableIndexMode(true).enableIndexMode(4)
                            .enableCustomDictionary(true).enableCustomDictionaryForcing(true)
                            .enableOffset(true).enableJapaneseNameRecognize(false)
                            .enableNameRecognize(false).enableAllNamedEntityRecognize(false)
                            .enableJapaneseNameRecognize(false)
                            .enableNumberQuantifierRecognize(false).enablePlaceRecognize(false)
                            .enableOrganizationRecognize(false)
                            .enableCustomDictionary(getDynamicCustomDictionary());
                }
            }
        }
        return segment;
    }

    /**
     * 获取动态自定义词典实例，采用双重检查锁定模式确保线程安全
     *
     * @return 自定义词典实例
     */
    public static DynamicCustomDictionary getDynamicCustomDictionary() {
        if (CustomDictionary == null) {
            synchronized (HanlpHelper.class) {
                if (CustomDictionary == null) {
                    // 创建自定义词典，使用 HanLP 配置的词典路径
                    CustomDictionary = new MultiCustomDictionary(HanLP.Config.CustomDictionaryPath);
                }
            }
        }
        return CustomDictionary;
    }

    /**
     * 重新加载自定义词典
     * 该方法会清除缓存文件，重置自定义词典路径，并重新加载词典
     *
     * @return 重新加载是否成功
     * @throws IOException 如果文件操作过程中发生IO异常
     */
    public static boolean reloadCustomDictionary() throws IOException {
        // 记录开始时间，用于计算加载耗时
        final long startTime = System.currentTimeMillis();

        // 检查词典路径是否有效
        if (HanLP.Config.CustomDictionaryPath == null
                || HanLP.Config.CustomDictionaryPath.length == 0) {
            return false;
        }

        // 根据不同的IO适配器选择不同的文件处理方式
        if (HanLP.Config.IOAdapter instanceof HadoopFileIOAdapter) {
            // 1.删除HDFS上的缓存文件
            HdfsFileHelper.deleteCacheFile(HanLP.Config.CustomDictionaryPath);
            // 2.查询文本文件，更新自定义词典路径
            HdfsFileHelper.resetCustomPath(getDynamicCustomDictionary());
        } else {
            // 处理本地文件系统
            FileHelper.deleteCacheFile(HanLP.Config.CustomDictionaryPath);
            FileHelper.resetCustomPath(getDynamicCustomDictionary());
        }
        // 3.清除前缀树缓存
        SearchService.clear();

        // 重新加载词典
        boolean reload = getDynamicCustomDictionary().reload();
        if (reload) {
            log.info("Custom dictionary has been reloaded in {} milliseconds",
                    System.currentTimeMillis() - startTime);
        }
        return reload;
    }

    /**
     * 重置HanLP配置，设置正确的词典文件路径
     *
     * @throws FileNotFoundException 如果找不到配置文件
     */
    private static void resetHanlpConfig() throws FileNotFoundException {
        // 如果使用Hadoop文件系统，则不需要重置配置
        if (HanLP.Config.IOAdapter instanceof HadoopFileIOAdapter) {
            return;
        }
        // 获取HanLP配置文件路径
        String hanlpPropertiesPath = getHanlpPropertiesPath();

        HanLP.Config.CustomDictionaryPath = Arrays.stream(HanLP.Config.CustomDictionaryPath)
                .map(path -> hanlpPropertiesPath + FILE_SPILT + path).toArray(String[]::new);
        log.info("hanlpPropertiesPath:{},CustomDictionaryPath:{}", hanlpPropertiesPath,
                HanLP.Config.CustomDictionaryPath);

        HanLP.Config.CoreDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.CoreDictionaryPath;
        HanLP.Config.CoreDictionaryTransformMatrixDictionaryPath = hanlpPropertiesPath + FILE_SPILT
                + HanLP.Config.CoreDictionaryTransformMatrixDictionaryPath;
        HanLP.Config.BiGramDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.BiGramDictionaryPath;
        HanLP.Config.CoreStopWordDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.CoreStopWordDictionaryPath;
        HanLP.Config.CoreSynonymDictionaryDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.CoreSynonymDictionaryDictionaryPath;
        HanLP.Config.PersonDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PersonDictionaryPath;
        HanLP.Config.PersonDictionaryTrPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PersonDictionaryTrPath;

        HanLP.Config.PinyinDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PinyinDictionaryPath;
        HanLP.Config.TranslatedPersonDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.TranslatedPersonDictionaryPath;
        HanLP.Config.JapanesePersonDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.JapanesePersonDictionaryPath;
        HanLP.Config.PlaceDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PlaceDictionaryPath;
        HanLP.Config.PlaceDictionaryTrPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PlaceDictionaryTrPath;
        HanLP.Config.OrganizationDictionaryPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.OrganizationDictionaryPath;
        HanLP.Config.OrganizationDictionaryTrPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.OrganizationDictionaryTrPath;
        HanLP.Config.CharTypePath = hanlpPropertiesPath + FILE_SPILT + HanLP.Config.CharTypePath;
        HanLP.Config.CharTablePath = hanlpPropertiesPath + FILE_SPILT + HanLP.Config.CharTablePath;
        HanLP.Config.PartOfSpeechTagDictionary =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PartOfSpeechTagDictionary;
        HanLP.Config.WordNatureModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.WordNatureModelPath;
        HanLP.Config.MaxEntModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.MaxEntModelPath;
        HanLP.Config.NNParserModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.NNParserModelPath;
        HanLP.Config.PerceptronParserModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PerceptronParserModelPath;
        HanLP.Config.CRFSegmentModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.CRFSegmentModelPath;
        HanLP.Config.HMMSegmentModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.HMMSegmentModelPath;
        HanLP.Config.CRFCWSModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.CRFCWSModelPath;
        HanLP.Config.CRFPOSModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.CRFPOSModelPath;
        HanLP.Config.CRFNERModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.CRFNERModelPath;
        HanLP.Config.PerceptronCWSModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PerceptronCWSModelPath;
        HanLP.Config.PerceptronPOSModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PerceptronPOSModelPath;
        HanLP.Config.PerceptronNERModelPath =
                hanlpPropertiesPath + FILE_SPILT + HanLP.Config.PerceptronNERModelPath;
    }

    /**
     * 获取HanLP配置文件的父目录路径
     *
     * @return HanLP配置文件的父目录路径
     * @throws FileNotFoundException 如果找不到配置文件
     */
    public static String getHanlpPropertiesPath() throws FileNotFoundException {
        return ResourceUtils.getFile("classpath:hanlp.properties").getParent();
    }

    /**
     * 向自定义词典中添加词条
     *
     * @param dictWord 要添加的词条对象，包含词语、词性和频率信息
     * @return 添加是否成功
     */
    public static boolean addToCustomDictionary(DictWord dictWord) {
        log.debug("dictWord:{}", dictWord);
        // 将词条插入到自定义词典中
        return getDynamicCustomDictionary().insert(dictWord.getWord(),
                dictWord.getNatureWithFrequency());
    }

    /**
     * 从自定义词典中移除指定词性的词条
     * 如果词条有多个词性，只移除指定的词性，保留其他词性
     *
     * @param dictWord 要移除的词条对象，包含词语和要移除的词性
     */
    public static void removeFromCustomDictionary(DictWord dictWord) {
        log.debug("dictWord:{}", dictWord);
        // 获取词条的属性信息
        CoreDictionary.Attribute attribute = getDynamicCustomDictionary().get(dictWord.getWord());
        if (attribute == null) {
            return;
        }
        log.info("get attribute:{}", attribute);

        // 先从词典中完全移除该词
        getDynamicCustomDictionary().remove(dictWord.getWord());

        // 构建保留其他词性的词条信息
        StringBuilder sb = new StringBuilder();
        List<Nature> natureList = new ArrayList<>();
        for (int i = 0; i < attribute.nature.length; i++) {
            // 只保留不等于要移除词性的其他词性
            if (!attribute.nature[i].toString().equals(dictWord.getNature())) {
                sb.append(attribute.nature[i].toString() + " ");
                sb.append(attribute.frequency[i] + " ");
                natureList.add((attribute.nature[i]));
            }
        }

        // 处理保留的词性信息
        String natureWithFrequency = sb.toString();
        int len = natureWithFrequency.length();
        log.info("filtered natureWithFrequency:{}", natureWithFrequency);

        // 如果还有其他词性，则重新添加词条
        if (StringUtils.isNotBlank(natureWithFrequency)) {
            getDynamicCustomDictionary().add(dictWord.getWord(),
                    natureWithFrequency.substring(0, len - 1));
        }

        // 从搜索服务中移除该词条
        SearchService.remove(dictWord, natureList.toArray(new Nature[0]));
    }

    /**
     * 转换小写字母形式的词条为原始形式
     * 处理自定义词典中的小写字母词条，将其转换为原始形式
     *
     * @param <T> MapResult的子类类型
     * @param mapResults 需要处理的映射结果列表
     */
    public static <T extends MapResult> void transLetterOriginal(List<T> mapResults) {
        if (CollectionUtils.isEmpty(mapResults)) {
            return;
        }

        List<T> newResults = new ArrayList<>();

        for (T mapResult : mapResults) {
            String name = mapResult.getName();
            boolean isAdded = false;
            // 检查是否是小写字母词条且在自定义词典中存在
            if (MultiCustomDictionary.isLowerLetter(name) && CustomDictionary.contains(name)) {
                CoreDictionary.Attribute attribute = CustomDictionary.get(name);
                if (attribute != null) {
                    // 添加原始形式的词条
                    isAdded = addLetterOriginal(newResults, mapResult, attribute);
                }
            }

            // 如果没有添加原始形式，则保留当前形式
            if (!isAdded) {
                newResults.add(mapResult);
            }
        }
        // 用新结果替换原列表
        mapResults.clear();
        mapResults.addAll(newResults);
    }

    /**
     * 根据词条属性添加原始形式的映射结果
     *
     * @param <T> MapResult的子类类型
     * @param mapResults 结果列表，用于添加新的映射结果
     * @param mapResult 当前处理的映射结果
     * @param attribute 词条属性，包含原始形式信息
     * @return 是否成功添加了原始形式
     */
    public static <T extends MapResult> boolean addLetterOriginal(List<T> mapResults, T mapResult,
            CoreDictionary.Attribute attribute) {
        if (attribute == null) {
            return false;
        }
        boolean isAdd = false;

        // 处理HanlpMapResult类型
        if (mapResult instanceof HanlpMapResult) {
            HanlpMapResult hanlpMapResult = (HanlpMapResult) mapResult;
            for (String nature : hanlpMapResult.getNatures()) {
                // 获取指定词性对应的原始形式
                String orig = attribute.getOriginal(Nature.fromString(nature));
                if (orig != null) {
                    // 创建新的映射结果并添加到结果列表
                    MapResult addMapResult = new HanlpMapResult(orig, Arrays.asList(nature),
                            hanlpMapResult.getDetectWord(), hanlpMapResult.getSimilarity());
                    mapResults.add((T) addMapResult);
                    isAdd = true;
                }
            }
            return isAdd;
        }

        // 获取所有原始形式
        List<String> originals = attribute.getOriginals();
        if (CollectionUtils.isEmpty(originals)) {
            return false;
        }

        // 处理DatabaseMapResult类型
        if (mapResult instanceof DatabaseMapResult) {
            DatabaseMapResult dbMapResult = (DatabaseMapResult) mapResult;
            for (String orig : originals) {
                // 创建新的数据库映射结果
                DatabaseMapResult addMapResult = new DatabaseMapResult();
                addMapResult.setName(orig);
                addMapResult.setSchemaElement(dbMapResult.getSchemaElement());
                addMapResult.setDetectWord(dbMapResult.getDetectWord());
                mapResults.add((T) addMapResult);
                isAdd = true;
            }
        }
        // 处理EmbeddingResult类型
        else if (mapResult instanceof EmbeddingResult) {
            EmbeddingResult embeddingResult = (EmbeddingResult) mapResult;
            for (String orig : originals) {
                // 创建新的嵌入向量映射结果
                EmbeddingResult addMapResult = new EmbeddingResult();
                addMapResult.setName(orig);
                addMapResult.setDetectWord(embeddingResult.getDetectWord());
                addMapResult.setId(embeddingResult.getId());
                addMapResult.setMetadata(embeddingResult.getMetadata());
                addMapResult.setSimilarity(embeddingResult.getSimilarity());
                mapResults.add((T) addMapResult);
                isAdd = true;
            }
        }

        return isAdd;
    }

    /**
     * 对文本进行分词并获取指定类型的词条
     *
     * @param text 要分词的文本
     * @param modelIdToDataSetIds 模型ID到数据集ID的映射关系
     * @return 分词结果列表，包含符合条件的S2Term对象
     */
    public static List<S2Term> getTerms(String text, Map<Long, List<Long>> modelIdToDataSetIds) {
        return getSegment().seg(text.toLowerCase()).stream()
                // 只保留以特定前缀开头的词性的词条
                .filter(term -> term.getNature().startsWith(DictWordType.NATURE_SPILT))
                // 将HanLP的Term转换为系统的S2Term
                .map(term -> transform2ApiTerm(term, modelIdToDataSetIds))
                // 展平结果列表
                .flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 根据数据集ID过滤词条列表
     *
     * @param terms 原始词条列表
     * @param dataSetIds 要保留的数据集ID集合
     * @return 过滤后的词条列表
     */
    public static List<S2Term> getTerms(List<S2Term> terms, Set<Long> dataSetIds) {
        // 记录原始词条
        logTerms(terms);

        // 如果有指定数据集ID，则进行过滤
        if (!CollectionUtils.isEmpty(dataSetIds)) {
            terms = terms.stream().filter(term -> {
                // 从词性中提取数据集ID
                Long dataSetId = NatureHelper.getDataSetId(term.getNature().toString());
                if (Objects.nonNull(dataSetId)) {
                    // 只保留指定数据集的词条
                    return dataSetIds.contains(dataSetId);
                }
                return false;
            }).collect(Collectors.toList());
            log.debug("terms filter by dataSetId:{}", dataSetIds);
            // 记录过滤后的词条
            logTerms(terms);
        }
        return terms;
    }

    /**
     * 将HanLP的Term对象转换为系统的S2Term对象
     * 处理词性映射，支持一个Term转换为多个S2Term
     *
     * @param term HanLP的Term对象
     * @param modelIdToDataSetIds 模型ID到数据集ID的映射关系
     * @return 转换后的S2Term列表
     */
    public static List<S2Term> transform2ApiTerm(Term term,
            Map<Long, List<Long>> modelIdToDataSetIds) {
        List<S2Term> s2Terms = Lists.newArrayList();
        // 将模型ID转换为数据集ID
        List<String> natures = NatureHelper.changeModel2DataSet(String.valueOf(term.getNature()),
                modelIdToDataSetIds);
        // 为每个转换后的词性创建一个S2Term对象
        for (String nature : natures) {
            S2Term s2Term = new S2Term();
            // 复制Term的属性到S2Term
            BeanUtils.copyProperties(term, s2Term);
            // 设置新的词性
            s2Term.setNature(Nature.create(nature));
            // 设置词频
            s2Term.setFrequency(term.getFrequency());
            s2Terms.add(s2Term);
        }
        return s2Terms;
    }

    /**
     * 记录词条列表的详细信息到日志
     *
     * @param terms 要记录的词条列表
     */
    private static void logTerms(List<S2Term> terms) {
        if (CollectionUtils.isEmpty(terms)) {
            return;
        }
        for (S2Term term : terms) {
            log.debug("word:{},nature:{},frequency:{}", term.word, term.nature.toString(),
                    term.getFrequency());
        }
    }
}
