package com.tencent.supersonic.headless.api.pojo;

/**
 * 模式元素类型枚举，用于标识Supersonic系统中不同类型的语义层元素。
 * 这些元素类型构成了系统的语义模型，用于自然语言查询解析和SQL生成过程中。
 */
public enum SchemaElementType {
    /**
     * 数据集类型，表示一个完整的数据集合，通常对应一个数据库表或视图。
     * 数据集是所有其他元素的容器，包含了指标、维度等元素。
     */
    DATASET,

    /**
     * 模型类型，表示一个数据模型，是数据集的逻辑分组。
     * 模型通常包含多个相关的数据集，用于组织和管理数据。
     */
    MODEL,

    /**
     * 度量类型，表示可以被聚合计算的数值型字段。
     * 度量通常用于度量业务表现，如销售额、用户数等，支持SUM、AVG、COUNT等聚合操作。
     */
    METRIC,

    /**
     * 维度类型，表示用于分组和筛选的非数值型字段。
     * 维度通常用于描述业务实体的属性，如用户的年龄、地区等。
     */
    DIMENSION,

    /**
     * 维度值类型，表示维度字段的具体取值。
     * 例如，对于"地区"维度，其值可能包括"北京"、"上海"等。
     */
    VALUE,

    /**
     * ID类型，表示唯一标识符字段。
     * 通常用于标识数据记录，如用户ID、订单ID等。
     */
    ID,

    /**
     * 日期类型，表示时间相关的字段。
     * 用于时间范围查询和时间序列分析，支持特殊的时间处理逻辑。
     */
    DATE,

    /**
     * 标签类型，表示用于分类和标记的字段。
     * 标签通常用于对数据进行额外的分类，可以应用于指标或维度。
     */
    TAG,

    /**
     * 术语类型，表示业务术语或概念。
     * 术语用于映射自然语言查询中的业务概念到具体的数据字段。
     */
    TERM
}