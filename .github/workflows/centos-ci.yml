name: supersonic CentOS CI
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    container:
      image: almalinux:9  # maven >=3.6.3

    strategy:
      matrix:
        java-version: [21]  # 定义要测试的JDK版本

    steps:
      - uses: actions/checkout@v2

      - name: Set up JDK ${{ matrix.java-version }}
        uses: actions/setup-java@v2
        with:
          java-version: ${{ matrix.java-version }}
          distribution: 'adopt'

      - name: Reset DNF repositories
        run: |
          sed -e 's|^mirrorlist=|#mirrorlist=|g' \
            -e 's|^# baseurl=https://repo.almalinux.org|baseurl=https://mirrors.aliyun.com|g' \
            /etc/yum.repos.d/almalinux*.repo


      - name: Update DNF package index
        run: dnf makecache

      - name: Install Maven with retry
        run: |
          for i in {1..5}; do
              dnf install -y maven && break || sleep 15
          done

      - name: Verify Java and Maven installation
        run: |
          java -version
          mvn -version

      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Build with Maven
        run: mvn -B package --file pom.xml

      - name: Test with Maven
        run: mvn test

