module.exports = {
  extends: [require.resolve('@umijs/fabric/dist/eslint')],
  // globals: {
  //   ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: true,
  //   page: true,
  //   REACT_APP_ENV: true,
  // },
  rules: {
    'spaced-comment': 'off',
    '@typescript-eslint/no-parameter-properties': 'off',
    '@typescript-eslint/no-redeclare': 'off',
    '@typescript-eslint/no-namespace': 'off',
    'no-param-reassign': 'off',
    'no-underscore-dangle': 'off',
    'no-restricted-syntax': 'off',
    '@typescript-eslint/no-loop-func': 'off',
    'consistent-type-definitions': 'off',
    "no-use-before-define": "off",
    "@typescript-eslint/no-use-before-define": "off",
    "@typescript-eslint/consistent-type-imports": "off",
    "no-shadow": "off",
    "@typescript-eslint/no-shadow": "off",
    'no-useless-return': 'off',
    'max-classes-per-file': 'off',
    'no-return-assign': 'off',
    'no-continue': 'off',
    'no-bitwise': 'off',
    'no-await-in-loop': 'off',
    '@typescript-eslint/no-unused-expressions': 'off',
    'global-require': 'off',
    'no-plusplus': 'off',
    'import/export': 'off',
    'react-hooks/exhaustive-deps': 'off',
    'import/no-extraneous-dependencies': 0,
    'react-hooks/exhaustive-deps': 0,
    'no-console': 0,
  },
};
