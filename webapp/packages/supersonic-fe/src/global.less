

:root:root {
  --tme-primary-color: #1672fa;
  --blue: #296df3;
  --deep-blue: #446dff;
  --chat-blue: #1b4aef;
  --body-background: #f7fafa;
  --deep-background: #f0f0f0;
  --light-background: #f5f5f5;
  --component-background: #fff;
  --header-color: #edf2f2;
  --text-color: #181a1a;
  --text-color-secondary: #3d4242;
  --text-color-third: #626a6a;
  --text-color-fourth: #889191;
  --text-color-fifth: #afb6b6;
  --text-color-six: #a3a4a6;
  --text-color-fifth-4: hsla(180, 5%, 70%, 0.4);
  --tooltip-max-width: 350px;
  --success-color: #52c41a;
  --processing-color: #ff2442;
  --error-color: #ff4d4f;
  --highlight-color: #ff4d4f;
  --font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",<PERSON> YaHei,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
  --tencent-font-family: 'tencentFont',-apple-system,BlinkMacSystemFont,"Segoe UI",Microsoft YaHei,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
}

html,
body,
#root {
  height: 100%;
  font-family: var(--tencent-font-family);
  -webkit-font-smoothing:antialiased;
  margin: 0;
  min-width: 1280px;
  overflow-x: scroll;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@font-face {
  font-family: 'DINPro Medium';
  src: url('~@/assets/fonts/DINPro.woff2') format('woff2');
}

@font-face {
  font-family: 'tencentFont';
  src: url('~@/assets/fonts/TencentSans-W7.woff') format('woff');
}

@media (max-width: 480px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead>tr,
    &-tbody>tr {

      >th,
      >td {
        white-space: pre;

        >span {
          display: block;
        }
      }
    }
  }
}

// 兼容IE11
@media screen and(-ms-high-contrast: active),
  (-ms-high-contrast: none) {
  body .ant-design-pro>.ant-layout {
    min-height: 100vh;
  }
}

// .ant-card-body {
//   padding: 24px !important;
// }

.ant-modal-body {
  padding: 24px;
}

// .ant-pro-page-container-children-content {
//   margin: 12px 12px 0 !important;
// }

// .ant-page-header {
//   padding-bottom: 10px !important;
// }

// .ant-spin-spinning {
//   display: flex !important;
//   align-items: center !important;
//   justify-content: center !important;
// }

// .ant-table-selection-extra {
//   .ant-dropdown-trigger {
//     display: none !important;
//   }
// }


.ant-pro-layout .ant-pro-layout-bg-list {
  background: #fafafb;
}
.ant-pro-layout .ant-pro-layout-content {
  padding: 0;
  background-color: #fafafb;
}


.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
}

.ant-spin .ant-spin-dot {
  font-size: 30px;
}

.ant-menu-root>.ant-menu-item.ant-menu-item-selected {
  color: var(--tme-primary-color);
  font-weight: 500;
}
.ant-menu-light.ant-menu-horizontal >.ant-menu-item:hover {
  background: #f0f5ff;
}
.ant-menu-light .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected):hover {
  color: var(--tme-primary-color);
}

.ant-menu-light.ant-menu-horizontal >.ant-menu-item-selected:hover::after {
  border-bottom-color: var(--tme-primary-color);
  border-bottom-width: 2px;
}
.ant-menu-light.ant-menu-horizontal >.ant-menu-item-selected::after {
    border-bottom-color: var(--tme-primary-color);
    border-bottom-width: 2px;
}

.ant-menu-light.ant-menu-horizontal >.ant-menu-item:hover::after {
  border-bottom-color: var(--tme-primary-color);
}

.ant-pro-table-list-toolbar-container-mobile {
  flex-direction: unset;
}

.logo {
  color: var(--tme-primary-color);
  font-family: var(--tencent-font-family);
  position: relative;
  font-size: 20px;
  font-weight: 700;
  padding-right: 50px;
}


.g6ContextMenuContainer {
  font-size: 12px;
  color: #545454;
  min-width: 100px;
  h3 {
    padding-bottom: 5px;
    margin: 0;
    border-bottom: 1px solid #4E86F5;
  }
  li {
    cursor: pointer;
    list-style-type:none;
    line-height: 25px;
    margin-left: 0;
    &:hover {
      color: #4E86F5;
    }
  }
  ul {
    width: 100%;
    padding: 0;
    margin: 0;
  }
  .ant-tag {
    transition: none;
  }
}

.semantic-graph-toolbar {
  position: absolute;
  width: 190px;
  height: 72px;
}

.g6-component-tooltip {
  p {
    line-height: 25px;
  }
}

.inherit-from-model-row{
  background-color: #e6edfc;

  .ant-table-cell-row-hover {
    background-color:#e6edfc!important;
  }
}

.ant-form-item .ant-form-item-label >label {
  font-weight: 500;
  color: #667085;
}

.ant-select-dropdown {
  .ant-select-item {
    -webkit-tap-highlight-color: transparent;
    background-color: transparent;
    outline: 0px;
    border: 0px;
    margin: 0px;
    border-radius: 0px;
    cursor: pointer;
    user-select: none;
    vertical-align: middle;
    appearance: none;
    color: #606060;
    font-size: 14px;
    line-height: 1.57;
    font-family: var(--tencent-font-family);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    text-decoration: none;
    min-height: 36px;
    padding: 6px 16px;
    box-sizing: border-box;
    white-space: nowrap;
    font-weight: 400;
  }
 &.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  font-weight: 400;
  color: rgb(38, 38, 38);
 }
}
.ant-form-item .ant-form-item-label > label {
  font-family: "tencentFont", sans-serif;
  margin-right: 10px;
}
.supersonicForm {
  padding: 0 20px;
  :global {
    .ant-form-item-label {
      .anticon {
        position: relative;
        top: 1px;
      }
    }
    .ant-input-search .ant-input-search-button {
      height: 42px;
    }
    .ant-switch.ant-switch-checked {
      background: var(--tme-primary-color);
    }
    .ant-form-item .ant-form-item-label > label {
      color:#262626;
    }
    .ant-form-item-label {
      font-family: "tencentFont", sans-serif;
      margin-right: 10px;
    }
    .ant-input {
      padding: 9px 14px 9px 12px;
    }
    .ant-input-affix-wrapper .ant-input {
      padding: 5px;
    }
    .ant-select {
      &.ant-select-single {
        height: auto;
      }
      &.ant-select-multiple {
        .ant-select-selector {
          padding: 6px 14px 6px 12px;
         }
      }
    .ant-select-selector {
      padding: 5px 14px 5px 12px;
      .ant-select-selection-placeholder {
        padding: 0px 8px 0px 8px;
      }
      .ant-select-selection-overflow-item {
        .ant-select-selection-item {
          color: rgb(22, 119, 255);
          background-color: rgb(230, 244, 255);
        }
      }
    }
  }
  }
}