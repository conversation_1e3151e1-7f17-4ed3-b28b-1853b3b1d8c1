@import '~@/assets/css/variable.less';

.loginWarp {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  .content {
    flex: 1;
    padding: 32px 0 24px;
    .formContent {
      display: flex;
      flex: 1 1;
      flex-direction: column;
      height: 100%;
      padding: 132px 0 24px;
      overflow: auto;
      background: inherit;
      .formBox {
        min-width: 480px;
        max-width: 500px;
        margin: 0 auto;
      }
    }
  }
}

.loginMain {
  // max-width: 480px;
  // min-height: 200px;
  margin: 120px auto auto;
  padding: 20px;
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 0 10px 2px #eee;
}

.title {
  margin-bottom: 20px;
  font-size: 24px;
  text-align: center;
}

.input {
  margin-bottom: 20px;
}

.signInBtn {
  width: 100%;
  margin: 20px 0;
  height: 40px;
}

.tool {
  display: flex;
  flex-direction: row-reverse;
}

.button {
  margin-left: 10px;
}
