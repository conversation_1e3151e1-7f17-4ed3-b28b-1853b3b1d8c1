.metricWrapper {
  width: 100%;
  margin-left: auto;
  box-sizing: border-box;
  margin-right: auto;
  padding-left: 16px;
  padding-right: 16px;
  position: relative;
  min-height: calc(-110px + 100vh);
  display: flex;
  flex-direction: column;
}

.metricFilterWrapper {
  margin: 20px;
  padding: 20px;
  padding-bottom: 5px;
  background: #fff;
  border-radius: 10px;
}

.metricTable {
  margin: 20px;
  :global {
    .ant-pro-card-body {
      padding: 0;
    }
  }
}

.table {
  overflow: auto;
}

.metricBasicInfo {
  .label {
    color: rgb(95, 116, 141);
    font-size: 14px;
  }
  div {
    min-width: 300px;
    margin-bottom: 10px;
    color: rgb(18, 31, 67);
    font-size: 15px;
  }
}

.caliberTitle {
  margin-bottom: 20px;
  color: rgb(18, 31, 67);
  font-weight: 500;
  font-size: 16px;
}

.caliberSubTitle {
  color: rgb(33, 43, 54);
  font-weight: 500;
  font-size: 14px;
}

.defineDataTable {
  width: 600px;
  overflow: hidden;
  color: rgb(67, 71, 75);
  background-color: rgb(255, 255, 255);
  border-radius: 6px;
  box-shadow: rgba(114, 117, 120, 0.14) 0px 9px 16px, rgba(179, 181, 183, 0.38) 0px 0px 2px;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  :global {
    .ant-table-thead {
      font-size: 13px;
    }
  }
}

.caliberDesc {
  color: #bdbdbd;
  font-size: 14px;
}

.searchBox {
  width: 740px;
  margin: 0 auto;
  // margin-bottom: 12px;
  background: #fff;
  border-radius: 10px;
  .searchInput {
    width: 100%;
    border: 1px solid rgba(35, 104, 184, 0.6);
    border-radius: 10px;
  }
  :global {
    .ant-select-auto-complete {
      width: 100%;
    }
    .ant-input {
      height: 50px;
      padding: 0 15px;
      color: #515a6e;
      font-size: 14px;
      line-height: 50px;
      background: hsla(0, 0%, 100%, 0.2);
      border: none;
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      caret-color: #296df3;

      &:focus {
        border-right-width: 0 !important;
        box-shadow: none;
      }
    }

    .ant-input-group-addon {
      left: 0 !important;
      padding: 0;
      background: hsla(0, 0%, 100%, 0.2);
      border: none;
      border-top-left-radius: 0;
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
      border-bottom-left-radius: 0;

      .ant-btn {
        width: 72px;
        height: 50px;
        margin: 0;
        color: rgba(35, 104, 184, 0.6);
        font-size: 16px;
        background-color: transparent;
        background-color: transparent;
        border: none;
        box-shadow: none !important;

        &::after {
          box-shadow: none !important;
        }

        .anticon {
          font-size: 28px;

          &:hover {
            color: @primary-color;
          }
        }
      }
    }
  }
}

.overviewExtraContainer {
  display: flex;
  font-size: 14px;
  .extraWrapper {
    display: flex;
    width: 100%;
    .extraStatistic {
      display: inline-flex;
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      color: rgba(42, 46, 54, 0.65);
      font-size: 14px;
      line-height: 1.5714285714285714;
      list-style: none;
      .extraTitle {
        // font-size: 12px;
        min-width: 50px;
        margin-bottom: 4px;
        color: rgba(42, 46, 54, 0.45);
        margin-inline-end: 6px;
        margin-block-end: 0;
      }
      .extraValue {
        display: inline-block;
        color: rgba(42, 46, 54, 0.65);
        font-size: 12px;
        direction: ltr;
      }
    }
  }
}

.metricEditWrapper {
  .metricDetailTab {
    :global {
      .ant-tabs-nav {
        margin: 10px 20px 0 20px;
        padding: 0 20px;
        background-color: rgb(255, 255, 255);
        border-radius: 8px;
        transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      }
      .ant-tabs-tab {
        padding: 12px 0;
        color: #344767;
        font-weight: 500;
      }
    }
  }
  .metricDetail {
    position: relative;
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0px;
    background-color: transparent;
    height: 100%;
    .tabContainer {
      padding: 24px;
      min-height: calc(100vh - 78px);
      width: calc(100vw - 350px);
      background-color: #fafafb;
    }
    .metricInfoContent {
      padding: 25px;
      .title {
        position: relative;
        margin-bottom: 12px;
        color: #0e73ff;
        font-weight: bold;
        font-size: 16px;
        &::before {
          position: absolute;
          top: 10px;
          left: -10px;
          display: block;
          width: 3px;
          height: 14px;
          font-size: 0;
          background: #0e73ff;
          border: 1px solid #0e73ff;
          border-radius: 2px;
          content: '';
        }
      }
    }
    .siderContainer {
      width: 350px;
      min-height: calc(100vh - 78px);
      border-radius: 6px;
      padding: 24px 0 24px 24px;
    }
  }
}

.metricDetailWrapper {
  height: calc(100vh - 56px);
  overflow: scroll;
  .metricDetailTab {
    :global {
      .ant-tabs-nav {
        margin: 10px 20px 0 20px;
        padding: 0 20px;
        background-color: rgb(255, 255, 255);
        border-radius: 8px;
        transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      }
      .ant-tabs-tab {
        padding: 12px 0;
        color: #344767;
        font-weight: 500;
      }
    }
  }
  .metricDetail {
    position: relative;
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0px;
    background-color: transparent;
    height: 100%;
    .tabContainer {
      height: 100%;
      min-height: calc(100vh - 78px);
      width: calc(100vw - 450px);
      background-color: #fafafb;
    }
    .metricInfoContent {
      padding: 25px;
      .title {
        position: relative;
        margin-bottom: 12px;
        color: #0e73ff;
        font-weight: bold;
        font-size: 16px;
        &::before {
          position: absolute;
          top: 10px;
          left: -10px;
          display: block;
          width: 3px;
          height: 14px;
          font-size: 0;
          background: #0e73ff;
          border: 1px solid #0e73ff;
          border-radius: 2px;
          content: '';
        }
      }
    }
    .siderContainer {
      width: 450px;
      min-height: calc(100vh - 78px);
      margin: 10px 20px 20px 0;
      background-color: rgb(255, 255, 255);
      border-radius: 6px;
      box-shadow: rgba(0, 0, 0, 0.08) 6px 0px 16px 0px, rgba(0, 0, 0, 0.12) 3px 0px 6px -4px,
        rgba(0, 0, 0, 0.05) 9px 0px 28px 8px;
    }
  }
}

.sectionBox {
  position: relative;
  margin: 20px;
  padding: 10px;
  overflow: hidden;
  background-color: rgb(255, 255, 255);
  background-image: none;
  border-radius: 6px;
  box-shadow: #888888 0px 0px 1px, rgba(29, 41, 57, 0.08) 0px 1px 3px;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.metricInfoSider {
  padding: 20px;
  color: #344767;
  background-color: #fff;
  height: 100%;
  border: 1px solid #e6ebf1;
  border-radius: 6px;
  .createTitle {
    margin-bottom: 10px;
    color: #344767;
    font-weight: 500;
    font-size: 16px;
    font-family: var(--tencent-font-family);
  }
  .gotoMetricListIcon {
    color: #3182ce;
    cursor: pointer;
    &:hover {
      color: #5493ff;
    }
  }
  .title {
    margin-bottom: 20px;
    .name {
      font-weight: 600;
      font-size: 18px;
    }
    .bizName {
      margin: 5px 0 0 25px;
      color: #7b809a;
      font-weight: 400;
    }
  }
  .desc {
    display: block;
    margin-top: 8px;
    color: #7b809a;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.9;
  }
  .subTitle {
    margin: 0px;
    color: rgb(123, 128, 154);
    font-weight: 700;
    font-size: 14px;
    line-height: 1.25;
    letter-spacing: 0.03333em;
    text-transform: uppercase;
    text-decoration: none;
    vertical-align: unset;
    opacity: 1;
  }
  .sectionContainer {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: scroll;

    overflow: hidden;
    background-image: none;
    border-radius: 6px;
    .section {
      padding: 16px;
      color: rgb(52, 71, 103);
      line-height: 1.25;
      background: transparent;
      box-shadow: none;
      opacity: 1;
      .sectionTitleBox {
        padding: 8px 0;
        color: rgb(52, 71, 103);
        background: transparent;
        box-shadow: none;
        opacity: 1;
        .sectionTitle {
          margin: 0px;
          color: rgb(52, 71, 103);
          font-weight: 600;
          font-size: 16px;
          line-height: 1.625;
          letter-spacing: 0.0075em;
          text-transform: capitalize;
          text-decoration: none;
          vertical-align: unset;
          opacity: 1;
        }
      }

      .item {
        display: flex;
        padding-top: 8px;
        padding-right: 16px;
        padding-bottom: 8px;
        color: rgb(52, 71, 103);
        background: transparent;
        box-shadow: none;
        opacity: 1;
        .itemLable {
          min-width: fit-content;
          margin: 0px;
          margin-right: 10px;
          color: #344767;
          font-weight: 700;
          font-size: 14px;
          line-height: 1.5;
          letter-spacing: 0.02857em;
          text-transform: capitalize;
          text-decoration: none;
          vertical-align: unset;
          opacity: 1;
        }
        .itemValue {
          margin: 0px;
          color: #7b809a;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.5;
          letter-spacing: 0.02857em;
          text-transform: none;
          text-decoration: none;
          vertical-align: unset;
          opacity: 1;
        }
      }
    }
    .hr {
      flex-shrink: 0;
      margin: 0px;
      border-color: rgb(242, 244, 247);
      // border-width: 0px 0px thin;
      border-style: solid;
    }
    .ctrlBox {
      .ctrlList {
        position: relative;
        margin: 0px;
        padding: 8px 0px;
        list-style: none;
        background-color: rgb(249, 250, 251);
        li {
          position: relative;
          display: flex;
          flex-grow: 1;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          min-width: 0px;
          margin: 4px;
          padding: 4px 16px;
          color: inherit;
          text-align: left;
          text-decoration: none;
          vertical-align: middle;
          background-color: transparent;
          border: 0px;
          border-radius: 0px;
          outline: 0px;
          cursor: pointer;
          transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
          appearance: none;
          user-select: none;
          -webkit-tap-highlight-color: transparent;
          -webkit-box-flex: 1;
          -webkit-box-pack: start;
          -webkit-box-align: center;
          &:hover {
            color: #3182ce;
            text-decoration: none;
            background-color: rgba(16, 24, 40, 0.04);
          }
        }
        .ctrlItemIcon {
          flex-shrink: 0;
          min-width: unset;
          margin-right: 5px;
          font-size: 14px;
        }
        .styles.ctrlItemLable {
          display: block;
          margin: 0px;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.6;
        }
      }
    }
  }
}

.settingList {
  list-style: none;
  margin: 0px;
  position: relative;
  padding: 0px;
  li {
    -webkit-tap-highlight-color: transparent;
    background-color: transparent;
    outline: 0px;
    border: 0px;
    margin: 0px;
    border-radius: 0px;
    cursor: pointer;
    user-select: none;
    vertical-align: middle;
    appearance: none;
    display: flex;
    flex-grow: 1;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    text-decoration: none;
    min-width: 0px;
    box-sizing: border-box;
    text-align: left;
    padding: 8px 16px;
    transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    &.active {
      background-color: rgba(22, 119, 255, 0.08);
      .icon {
        color: rgb(22, 119, 255);
      }
      .content {
        .text {
          color: rgb(22, 119, 255);
        }
      }
    }
    .icon {
      min-width: 32px;
      color: #344767;
      flex-shrink: 0;
      display: inline-flex;
    }
    .content {
      flex: 1 1 auto;
      min-width: 0px;
      margin-top: 4px;
      margin-bottom: 4px;
      .text {
        margin: 0px;
        color: #344767;
        font-size: 16px;
        // line-height: 1.57;
        // font-family: var(--tencent-font-family);
        font-weight: 600;
        display: block;
      }
    }
    &:hover {
      text-decoration: none;
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}
