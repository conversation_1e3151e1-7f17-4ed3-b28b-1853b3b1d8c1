.TagFilterWrapper {
  margin: 20px;
  padding: 20px;
  padding-bottom: 5px;
  background: #fff;
  border-radius: 10px;
}

.tagTable {
  margin: 20px;
  :global {
    .ant-pro-card-body {
      padding: 0;
    }
  }
}

.searchBox {
  width: 740px;
  margin: 0 auto;
  // margin-bottom: 12px;
  background: #fff;
  border-radius: 10px;
  .searchInput {
    width: 100%;
    border: 1px solid rgba(35, 104, 184, 0.6);
    border-radius: 10px;
  }
  :global {
    .ant-select-auto-complete {
      width: 100%;
    }
    .ant-input {
      height: 50px;
      padding: 0 15px;
      color: #515a6e;
      font-size: 14px;
      line-height: 50px;
      background: hsla(0, 0%, 100%, 0.2);
      border: none;
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      caret-color: #296df3;

      &:focus {
        border-right-width: 0 !important;
        box-shadow: none;
      }
    }

    .ant-input-group-addon {
      left: 0 !important;
      padding: 0;
      background: hsla(0, 0%, 100%, 0.2);
      border: none;
      border-top-left-radius: 0;
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
      border-bottom-left-radius: 0;

      .ant-btn {
        width: 72px;
        height: 50px;
        margin: 0;
        color: rgba(35, 104, 184, 0.6);
        font-size: 16px;
        background-color: transparent;
        background-color: transparent;
        border: none;
        box-shadow: none !important;

        &::after {
          box-shadow: none !important;
        }

        .anticon {
          font-size: 28px;

          &:hover {
            color: @primary-color;
          }
        }
      }
    }
  }
}

.tagDetailWrapper {
  height: calc(100vh - 56px);
  overflow: scroll;
  .tagDetailTab {
    :global {
      .ant-tabs-nav {
        margin: 10px 20px 0 20px;
        padding: 0 20px;
        background-color: rgb(255, 255, 255);
        border-radius: 8px;
        transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      }
      .ant-tabs-tab {
        padding: 12px 0;
        color: #344767;
        font-weight: 500;
      }
    }
  }
  .tagDetail {
    position: relative;
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0px;
    background-color: transparent;
    .tabContainer {
      height: 100%;
      width: calc(100vw - 450px);
      background-color: rgb(240, 242, 245);
    }
    .metricInfoContent {
      padding: 25px;
      .title {
        position: relative;
        margin-bottom: 12px;
        color: #0e73ff;
        font-weight: bold;
        font-size: 16px;
        &::before {
          position: absolute;
          top: 10px;
          left: -10px;
          display: block;
          width: 3px;
          height: 14px;
          font-size: 0;
          background: #0e73ff;
          border: 1px solid #0e73ff;
          border-radius: 2px;
          content: '';
        }
      }
    }
    .siderContainer {
      width: 450px;
      min-height: calc(100vh - 78px);
      margin: 10px 20px 20px 0;
      background-color: rgb(255, 255, 255);
      border-radius: 6px;
      box-shadow: rgba(0, 0, 0, 0.08) 6px 0px 16px 0px, rgba(0, 0, 0, 0.12) 3px 0px 6px -4px,
        rgba(0, 0, 0, 0.05) 9px 0px 28px 8px;
    }
  }
}

.sectionBox {
  position: relative;
  margin: 20px;
  padding: 10px;
  overflow: hidden;
  background-color: rgb(255, 255, 255);
  background-image: none;
  border-radius: 6px;
  box-shadow: #888888 0px 0px 1px, rgba(29, 41, 57, 0.08) 0px 1px 3px;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.metricInfoSider {
  padding: 24px;
  color: #344767;
  .gotoMetricListIcon {
    color: #3182ce;
    cursor: pointer;
    &:hover {
      color: #5493ff;
    }
  }
  .title {
    .name {
      font-weight: 600;
      font-size: 18px;
    }
    .bizName {
      margin: 5px 0 0 25px;
      color: #7b809a;
      font-weight: 400;
    }
  }
  .desc {
    display: block;
    margin-top: 8px;
    color: #7b809a;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.9;
  }
  .subTitle {
    margin: 0px;
    color: rgb(123, 128, 154);
    font-weight: 700;
    font-size: 14px;
    line-height: 1.25;
    letter-spacing: 0.03333em;
    text-transform: uppercase;
    text-decoration: none;
    vertical-align: unset;
    opacity: 1;
  }
  .sectionContainer {
    margin-top: 20px;
    overflow: hidden;
    // box-shadow: #888888 0px 0px 1px, rgba(29, 41, 57, 0.08) 0px 1px 3px;
    background-image: none;
    // transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    border-radius: 6px;
    .section {
      padding: 16px;
      color: rgb(52, 71, 103);
      line-height: 1.25;
      background: transparent;
      box-shadow: none;
      opacity: 1;
      .sectionTitleBox {
        padding: 8px 0;
        color: rgb(52, 71, 103);
        background: transparent;
        box-shadow: none;
        opacity: 1;
        .sectionTitle {
          margin: 0px;
          color: rgb(52, 71, 103);
          font-weight: 600;
          font-size: 16px;
          line-height: 1.625;
          letter-spacing: 0.0075em;
          text-transform: capitalize;
          text-decoration: none;
          vertical-align: unset;
          opacity: 1;
        }
      }

      .item {
        display: flex;
        padding-top: 8px;
        padding-right: 16px;
        padding-bottom: 8px;
        color: rgb(52, 71, 103);
        background: transparent;
        box-shadow: none;
        opacity: 1;
        .itemLable {
          min-width: fit-content;
          margin: 0px;
          margin-right: 10px;
          color: #344767;
          font-weight: 700;
          font-size: 14px;
          line-height: 1.5;
          letter-spacing: 0.02857em;
          text-transform: capitalize;
          text-decoration: none;
          vertical-align: unset;
          opacity: 1;
        }
        .itemValue {
          margin: 0px;
          color: #7b809a;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.5;
          letter-spacing: 0.02857em;
          text-transform: none;
          text-decoration: none;
          vertical-align: unset;
          opacity: 1;
        }
      }
    }
    .hr {
      flex-shrink: 0;
      margin: 0px;
      border-color: rgb(242, 244, 247);
      // border-width: 0px 0px thin;
      border-style: solid;
    }
    .ctrlBox {
      .ctrlList {
        position: relative;
        margin: 0px;
        padding: 8px 0px;
        list-style: none;
        background-color: rgb(249, 250, 251);
        li {
          position: relative;
          display: flex;
          flex-grow: 1;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          min-width: 0px;
          margin: 4px;
          padding: 4px 16px;
          color: inherit;
          text-align: left;
          text-decoration: none;
          vertical-align: middle;
          background-color: transparent;
          border: 0px;
          border-radius: 0px;
          outline: 0px;
          cursor: pointer;
          transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
          appearance: none;
          user-select: none;
          -webkit-tap-highlight-color: transparent;
          -webkit-box-flex: 1;
          -webkit-box-pack: start;
          -webkit-box-align: center;
          &:hover {
            color: #3182ce;
            text-decoration: none;
            background-color: rgba(16, 24, 40, 0.04);
          }
        }
        .ctrlItemIcon {
          flex-shrink: 0;
          min-width: unset;
          margin-right: 5px;
          font-size: 14px;
        }
        .styles.ctrlItemLable {
          display: block;
          margin: 0px;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.6;
        }
      }
    }
  }
}
