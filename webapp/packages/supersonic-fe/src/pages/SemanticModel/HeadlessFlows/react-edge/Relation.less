.relation-count-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: #868c91;
  background-color: #d8d8d8;
  cursor: pointer;
  &:hover {
    color: #fff;
    background-color: #1890ff;
  }
}

.relation-operation-popover .ant-popover-inner-content {
  padding: 0;
}

.relation-operation-container {
  width: 220px;
  max-height: 80px;
  padding: 12px 16px;
  overflow: hidden;
  background-color: #fff;

  &:hover {
    overflow-y: auto;
  }

  &::-webkit-scrollbar {
    width: 5px;
    background: #2b2f33;
  }

  &::-webkit-scrollbar-thumb {
    background: #5f656b;
    border-radius: 10px;
  }

  .relation-operation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 28px;
    color: #000;
    .relation-operation-item-content {
      display: flex;
      flex-basis: 160px;
      align-items: center;
      justify-content: space-between;
      height: 100%;

      // &:hover {
      //   cursor: pointer;
      //   background: #d8d8d8
      // }
    }
    .relation-property-source,
    .relation-property-target {
      display: inline-block;
      max-width: 65px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .relation-property-source {
      padding-right: 6px;
    }
    .relation-property-target {
      padding-left: 6px;
    }
  }
}
