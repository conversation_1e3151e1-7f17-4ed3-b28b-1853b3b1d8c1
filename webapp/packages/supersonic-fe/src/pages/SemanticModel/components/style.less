.projectBody {
  display: flex;
  flex-direction: row;
  background-color: #fff;
  height: calc(100vh - 56px);


  .projectManger {
    border-top: 1px solid #eee;
    width: 100%;
    min-height: calc(100vh - 56px);
    background-color: #fff;
    display: flex;
    position: relative;
    .sider {
      flex: 0 0 auto;
      width: 250px;
      border-right: 5px solid #eee;
      position: relative;
      transition: all 0.2s,background 0s;
      .treeContainer {
        width: 100%;
        opacity: 1;
        transition: all 0.2s,background 0s;
      }
      &.siderCollapsed {
        width: 20px;
        & .treeContainer{
          opacity: 0;
        }
      }
      .siderCollapsedButton {
        position: absolute;
        inset-block-start: 17px;
        border: 1px solid #eee;
        cursor: pointer;
        z-index: 101;
        width: 24px;
        height: 24px;
        text-align: center;
        border-radius: 40px;
        inset-inline-end: -13px;
        transition: transform 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: rgba(0, 0, 0, 0.25);
        background-color: #ffffff;
        box-shadow: 0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08);
        &:hover {
          color: rgba(0, 0, 0, 0.75);
        }
      }
      .domainTitle {
        margin-bottom: 0;
        font-size: 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        font-weight: 500;
        padding: 12px 0 12px 35px;
      }

      .addBtn {
        cursor: pointer;
        width: 100%;
        font-size: 18px;
        margin-top: 18px;
        &:hover {
          color: #296DF3;
        }
      }
      .treeTitle {
        margin-bottom: 0;
        padding: 20px;
        line-height: 34px;
        text-align: right;
        background: #fff;

        .title {
          float: left;
        }
      }
      .search {
        width: 175px;
        margin: 10px 0 10px 16px;
      }
      .projectItem {
        display: flex;
        width: 100%;
        cursor: auto;
        line-height: 25px;
        height: 25px;
        color: rgb(95, 116, 141);
        font-weight: 500;
        .projectItemTitle {
          font-size: 15px;
          flex: 1;
          cursor: pointer;
        }
        .operation {
          .icon {
            margin-left: 6px;
            cursor: pointer;
          }
        }
        .rowHover {
          display: none;
        }
        &:hover {
          .rowHover {
            display: block;
            color: var(--tme-primary-color);
          }
        }
      }
    }
    .content {
      flex: 1 1 auto;
      height: calc(100vh - 56px);
      overflow: scroll;
      padding-bottom: 20px;
    }
    .collapseLeftBtn {
      position: absolute;
      top: calc(50% + 45px);
      left: 0;
      z-index: 100;
      display: flex;
      align-items: center;
      height: 70px;
      color: #fff;
      font-size: 12px;
      background-color: rgba(40, 46, 54, 0.2);
      border-radius: 0 24px 24px 0;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .title {
      margin-bottom: 0;
      // padding: 20px;
      font-size: 20px;
      line-height: 34px;
      border-bottom: 1px solid #d9d9d9;
      display: flex;
    }
    .navContainer {
      padding: 20px;
      padding-left: 30px;
    }
    // .backBtn {
    //   background-color: #f8f8f8;
    //   padding: 5px;
    //   color: #b0b4bc;
    //   cursor: pointer;
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    //   &:hover {
    //     background-color: #7599e5;
    //     color: #fff;
    //   }
    // }
    .tab {

      // margin-top: 10px;
      :global {
        .ant-tabs-tab-btn {
          font-size: 16px;
        }
        .ant-tabs-nav-wrap {
          padding: 0 20px;
        }
        .ant-tabs-nav {
          margin-bottom: 0;
        }
        .ant-tabs-tab {
          padding-bottom:15px;
          color: rgb(95, 116, 141);
          font-weight: 500;
        }
      }
      .backBtn {
        height: 56px;
        background-color: #f8f8f8;
        padding: 5px;
        color: #b0b4bc;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        &:hover {
          background-color: #7599e5;
          color: #fff;
        }
      }
    }
    .chatSettingSectionTab {
      :global {
        .ant-tabs-tab-btn {
          font-size: 14px;
        }
        .ant-tabs-nav-wrap {
          padding: 0;
        }
      }
    }

    .mainTip {
      padding: 20px;
    }

    :global {
      .ant-card-body {
        padding: 0 !important;
      }
    }


  }
}



.domainList {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  .searchContainer {
    width: 250px;
    padding:3px 0;
    border-bottom: 1px solid #eee;
  }
}


.disabledSearchTable {
  padding-top:20px;
}

.classTable {
  // padding: 0 20px;
  :global {
    .ant-pro-table-search-query-filter {
      margin-bottom: 0;
    }
    .ant-pro-query-filter {
      padding-bottom: 10px;
    }
    .ant-pro-table-list-toolbar-container {
      padding-top: 0;
    }
    .ant-table-tbody > tr.ant-table-row-selected > td {
      background: none;
    }
  }
}

.classTableSelectColumnAlignLeft {
  :global {
    .ant-table-selection-column {
      text-align: left;
    }
    .ant-pro-card-body {
      padding: 0;
    }
  }
}

.permissionDrawer {
  :global {
    .ant-drawer-body {
      background: #f8f9fb;
    }
  }
}


.domainSelector {
  :global {
    .ant-select-tree .ant-select-tree-treenode-active .ant-select-tree-node-content-wrapper {
      outline: none;
    }
  }
}


.infoTagList{
  .siteTagPlus {
    background: #fff;
    border-style: dashed;
  }
  .editTag {
    user-select: none;
  }
  .tagInput {
    width: 78px;
    margin-right: 8px;
    vertical-align: top;
  }
  [data-theme="dark"] .siteTagPlus {
    background: transparent;
    border-style: dashed;
  }
}

.semanticGraphCanvas {
  position: relative;
  .toolbar{
    position: absolute;
    width: 200px;
    z-index: 999;
    right: 0;
    top: 5px;
  }
  .canvasContainer {
    width: 100%;
  }
}

.taskStateRefreshIcon {
  cursor: pointer;
  &:hover {
    color: #296DF3;
  }
}

.ctrlBtnContainer {
  :global {
    .ant-btn-link {
      padding: 0;
    }
  }
}

.breadcrumb{
  font-size: 18px;
  height: 48px;
  line-height: 48px;
  padding: 0 20px;
  :global {
    .ant-breadcrumb-link {
      height: 28px;
      color: #709bf1;
      cursor: pointer;
      &:hover{
        color: #296df3;
      }
    }
    .anticon {
      font-size: 18px;
    }
    li {
      &:last-child {
        .ant-breadcrumb-link {
          color: #296df3;
        }

      }
    }
  }
}

.classDataSourceTypeModal {
  :global {
    .ant-modal-body{
      padding: 0px;
    }
  }
}

.desc {
  margin: 0;
  // padding: 25px;
  color: #667085;
  font-size: 14px;
  // border-bottom: 1px solid #eee;
  // border-top: 1px solid #eee;
  // margin-bottom: 10px;
  .markerTag {
    color: #2499ef;
    font-size: 14px;
    margin: 0 2px;
  }
}

.textLink {
  color: #101828;
  &:hover {
    color: #69b1ff;
  }
}

.tableHeaderTitle {
  margin-left: 10px;
  .headerTitleLabel {
    color: #667085;
    font-size: 14px;
    font-weight: 400;
    width: max-content;
    display: flex;
  }
}


.infoCard {
    min-height: 100%;
    background-color: rgb(255, 255, 255);
    color: rgb(38, 38, 38);
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    position: relative;
    border: 1px solid rgb(230, 235, 241);
    border-radius: 4px;
    box-shadow: inherit;
    .infoCardTitle {
      display: flex;
      border-bottom: 1px solid #e6ebf1;
      padding: 15px 20px;
      align-items: center;
      color: rgb(38, 38, 38);
      margin: 0px;
      font-size: 16px;
      font-weight: 600;
      line-height: 1.57;
      font-family: "tencentFont", sans-serif;
    }
    .infoCardContainer {
      padding: 20px;
      height: calc(100vh - 260px);
      overflow: scroll;
    }
    .infoCardFooter {
      border-top: 1px solid #e6ebf1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex: 0 0 auto;
      padding: 20px;
      .infoCardFooterContainer {
        box-sizing: border-box;
        display: flex;
        flex-flow: wrap;
        // width: 100%;
        justify-content: space-between;
        align-items: center;
      }
    }
}



.dimensionValueFilterTable {
  :global {
    .ant-pro-card-body {
      padding:0
    }
  }
}