
.standardFormRow {
  display: flex;
  width: 100%;
  margin-bottom: 16px;
  // padding-bottom: 16px;
  // border-bottom: 1px dashed @border-color-split;
  :global {
    .ant-form-item,
    .ant-legacy-form-item {
      margin-right: 24px;
    }
    .ant-form-item-label,
    .ant-legacy-form-item-label {
      label {
        margin-right: 0;
        color: fade(#000, 85%);
      }
    }
    .ant-form-item-label,
    .ant-legacy-form-item-label,
    .ant-form-item-control,
    .ant-legacy-form-item-control {
      padding: 0;
      line-height: 32px;
    }
  }
  .label {
    flex: 0 0 auto;
    margin-right: 12px;
    color:  fade(#000, 85%);
    font-size: 14px;
    text-align: left;
    & > span {
      display: inline-block;
      flex-shrink: 0;
      // width: 80px;
      height: 32px;
      // height: 20px;
      color: #999;
      font-weight: 500;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      line-height: 32px;
      // line-height: 20px;
      // &::after {
      //   content: '：';
      // }
    }
  }
  .content {
    flex: 1 1 0;
    :global {
      .ant-form-item,
      .ant-legacy-form-item {
        &:last-child {
          display: block;
          margin-right: 0;
        }
      }
    }
  }
}

.standardFormRowLast {
  margin-bottom: 0;
  padding-bottom: 0;
  border: none;
}

.standardFormRowBlock {
  :global {
    .ant-form-item,
    .ant-legacy-form-item,
    div.ant-form-item-control-wrapper,
    div.ant-legacy-form-item-control-wrapper {
      display: block;
    }
  }
}

.standardFormRowGrid {
  :global {
    .ant-form-item,
    .ant-legacy-form-item,
    div.ant-form-item-control-wrapper,
    div.ant-legacy-form-item-control-wrapper {
      display: block;
    }
    .ant-form-item-label,
    .ant-legacy-form-item-label {
      float: left;
    }
  }
}
