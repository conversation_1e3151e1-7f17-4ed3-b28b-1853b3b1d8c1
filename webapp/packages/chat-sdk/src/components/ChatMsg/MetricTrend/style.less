@import '../../../styles/index.less';

@metric-trend-prefix-cls: ~'@{supersonic-chat-prefix}-metric-trend';

@metric-info-prefix-cls: ~'@{supersonic-chat-prefix}-metric-info';

.@{metric-trend-prefix-cls} {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 4px;
  width: 100%;
  row-gap: 4px;

  &-top-bar {
    display: flex;
    align-items: baseline;
    flex-wrap: wrap;
    row-gap: 12px;
  }

  &-filter-section-wrapper {
    display: flex;
    align-items: center;
    color: var(--text-color-third);
    margin-left: 4px;
  }

  &-filter-section {
    display: flex;
    align-items: center;
    font-size: 13px;
    column-gap: 12px;
    color: var(--text-color-third);
  }

  &-filter-item {
    display: flex;
    align-items: center;
  }

  &-filter-item-label {
    color: var(--text-color-third);
  }

  &-filter-item-value {
    color: var(--text-color);
    font-weight: 500;
  }

  &-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    row-gap: 12px;
  }

  &-select-options {
    display: flex;
    justify-content: space-between;
  }

  &-indicator {
    display: flex;
    flex-direction: column;
    align-items: baseline;
    justify-content: center;
  }

  &-date-range {
    color: var(--text-color-fourth);
    font-size: 14px;
  }

  &-indicator-value {
    color: var(--text-color);
    font-weight: 600;
    font-size: 30px;
  }

  &-indicator-name {
    color: var(--text-color-fourth);
    font-size: 14px;
  }

  &-flow-trend-chart {
    margin-top: 4px;
    height: 230px;
  }

  &-flow-trend-chart-single {
    height: 180px;
  }

  &-charts {
    display: flex;
    flex-direction: column;
    width: 100%;
    row-gap: 8px;
  }

  &-metric-fields {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    row-gap: 12px;
    color: var(--text-color);
    font-size: 15px;
  }

  &-metric-field {
    display: inline-block;
    box-sizing: border-box;
    height: auto;
    margin: 0;
    margin-right: 8px;
    padding: 1px 8px;
    color: var(--text-color-third);
    font-variant: tabular-nums;
    line-height: 20px;
    white-space: nowrap;
    list-style: none;
    border-color: transparent;
    border-radius: 2px;
    cursor: pointer;
    opacity: 1;
    transition: all 0.3s;
    font-feature-settings: 'tnum', 'tnum';

    &:hover {
      color: var(--chat-blue);
    }
  }

  &-metric-field-single {
    padding-left: 0;
    font-weight: 500;
    cursor: default;
    font-size: 15px;
    color: var(--text-color);

    &:hover {
      color: var(--text-color);
    }
  }
}

.@{metric-info-prefix-cls} {
  &-indicator {
    display: flex;
    align-items: baseline;
    column-gap: 12px;
  }

  &-indicator-value {
    color: var(--text-color);
    font-weight: 500;
    font-size: 28px;
    line-height: 40px;
    margin-top: 2px;
    color: var(--text-color-secondary);
  }

  &-bottom-section {
    display: flex;
    align-items: center;
    column-gap: 20px;
    margin-top: 4px;
  }

  &-date {
    color: var(--text-color-fourth);
    font-size: 13px;
  }

  &-date-value {
    color: var(--chat-blue);
  }

  &-indicator-switch {
    color: var(--text-color-fourth);
    font-size: 18px;
    margin-left: 6px;
    margin-bottom: 3px;
  }

  &-period-compare {
    display: flex;
    align-items: center;
    column-gap: 20px;
    font-size: 13px;
    overflow-x: auto;
  }
}
