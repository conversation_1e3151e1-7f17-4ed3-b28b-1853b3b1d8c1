Apache License Version 2.0

Copyright (2025) The SuperSonic Project Authors.  All rights reserved.

----------

SuperSonic is licensed under the Apache License 2.0, with the following additional conditions:

1. The commercial usage of SuperSonic:

a. SuperSonic may be utilized commercially, including as a frontend and backend service without modifying the source
code and logo.

b. a commercial license must be obtained from the author if you want to develop and distribute a derivative work based
on SuperSonic.

<NAME_EMAIL> by email to inquire about licensing matters.


2. As a contributor, you should agree that:

a. The producer can adjust the open-source agreement to be more strict or relaxed as deemed necessary.

b. Your contributed code may be used for commercial purposes, including but not limited to its cloud edition.

Apart from the specific conditions mentioned above, all other rights and restrictions follow the Apache License 2.0.
Detailed information about the Apache License 2.0 can be found at http://www.apache.org/licenses/LICENSE-2.0.

----------

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.